# STM32F407 数字手表项目

## 项目简介
基于STM32F407VET6开发板和0.96寸IIC OLED显示屏的数字手表程序，实现时间、日期和星期的实时显示。

## 功能特性

### 核心功能
- **时间显示**: 24小时制格式 (HH:MM:SS)，大字体居中显示
- **日期显示**: 年-月-日格式 (20YY-MM-DD)，包含星期信息
- **星期显示**: 英文缩写 (<PERSON>, <PERSON><PERSON>, <PERSON>d, <PERSON>hu, <PERSON><PERSON>, <PERSON><PERSON>, Sun)
- **自动日期更新**: 支持闰年计算和月份天数自动调整

### 增强功能
- **电池电量显示**: 文字形式电量百分比显示
- **温度显示**: 实时温度监测（模拟数据）
- **步数计数**: 运动步数统计显示
- **多主题切换**: 3种不同的边框主题自动切换
- **单界面设计**: 专为小屏幕优化，避免花屏问题

### 视觉效果
- **开机动画**: 多阶段启动动画，包括品牌logo、进度条、欢迎信息
- **简化指示器**: 简洁的秒数指示效果（圆圈、点闪烁）
- **装饰边框**: 3种主题边框样式（简约、圆角、双线）
- **优化布局**: 针对0.96寸小屏幕优化的显示布局

## 硬件要求
- STM32F407VET6开发板
- 0.96寸IIC OLED显示屏 (SSD1306)
- IIC连接线

## 连接方式
| OLED引脚 | STM32引脚 | 功能 |
|----------|-----------|------|
| VCC      | 3.3V      | 电源 |
| GND      | GND       | 地线 |
| SCL      | PB10      | IIC时钟线 |
| SDA      | PB11      | IIC数据线 |

## 软件架构
- **软件时钟**: 使用软件计时器实现时间计算，避免硬件RTC依赖
- **闰年支持**: 自动识别闰年并调整2月天数
- **月份管理**: 自动处理不同月份的天数差异
- **星期计算**: 自动更新星期信息

## 界面布局

### 主界面（简化版）
```
23°C         BAT:85%    <- 温度显示    电池电量
=================       <- 简化边框
   18:20:00      ◯      <- 时间显示    指示器
   08-12 Tue           <- 日期和星期
Steps:1234             <- 步数计数
=================
```

**布局说明：**
- 顶部：温度和电池电量显示
- 中部：大字体时间显示 + 简单指示器
- 下部：日期星期和步数统计
- 边框：简化的装饰边框，避免花屏

## 初始设置
程序默认设置为：
- 时间: 18:20:00
- 日期: 2025年8月12日
- 星期: 星期二

可在代码中修改 `SoftClock_t clock` 初始值来设置不同的起始时间。

## 编译和烧录
1. 使用Keil MDK-ARM打开项目文件
2. 编译项目
3. 通过ST-Link或其他调试器烧录到开发板

## 代码优化特点
- **最少行数实现**: 核心功能控制在最少代码行数
- **右侧注释**: 所有注释采用行尾注释格式
- **中文友好**: 支持中文注释和说明
- **性能优化**: 每秒更新一次，减少不必要的刷新

## 使用说明

### 开机流程
1. **第一阶段**: 显示手表图标和"Smart Watch"品牌信息（1.5秒）
2. **第二阶段**: 显示"Loading..."和进度条动画（1.5秒）
3. **第三阶段**: 显示"Welcome! Enjoy your day"欢迎信息（2秒）
4. **淡出效果**: 闪烁3次后进入主界面

### 界面操作
1. **单界面显示**: 只显示主界面，避免小屏幕切换导致的花屏问题
2. **主题切换**: 每60秒自动切换装饰主题（简约→圆角→双线→简约）
3. **实时更新**: 时间、日期、状态信息每秒自动更新
4. **简化动画**: 简洁的指示器动画，减少屏幕负担

### 数据显示
- **时间**: 24小时制，精确到秒，大字体显示
- **日期**: 简化日期格式（MM-DD 星期），节省空间
- **电池**: 文字显示，支持0-100%范围
- **温度**: 摄氏度显示，模拟环境温度
- **步数**: 累计步数统计，支持0-9999范围

## 技术特点

### 动画系统
- **帧动画**: 基于帧计数器的平滑动画系统
- **多种效果**: 圆圈、旋转、波形、数字雨等多种视觉效果
- **性能优化**: 动画计算优化，避免复杂数学运算

### 界面管理
- **状态机**: 基于枚举的界面状态管理
- **模块化**: 每个界面独立的显示函数
- **数据分离**: 界面显示与数据逻辑分离

### 代码优化
- **最少行数**: 核心功能控制在最少代码行数
- **右侧注释**: 所有注释采用行尾注释格式
- **中文友好**: 支持中文注释和说明
- **性能优化**: 每秒更新一次，减少不必要的刷新

## 扩展功能建议
- **按键控制**: 添加按键设置时间和切换界面功能
- **闹钟功能**: 添加多组闹钟设置和提醒
- **传感器集成**: 集成真实的温度、加速度传感器
- **蓝牙连接**: 添加蓝牙通信功能
- **省电模式**: 添加屏幕休眠和低功耗模式
- **自定义主题**: 用户可自定义界面主题和布局
- **数据存储**: 添加EEPROM存储用户设置和历史数据

## 注意事项
- 本程序使用软件时钟，断电后时间会重置
- 如需保持时间，建议添加硬件RTC支持
- OLED显示屏需要正确连接IIC引脚
- 确保电源供应稳定