# STM32F407 数字手表项目

## 项目简介
基于STM32F407VET6开发板和0.96寸IIC OLED显示屏的数字手表程序，实现时间、日期和星期的实时显示。

## 功能特性
- **时间显示**: 24小时制格式 (HH:MM:SS)
- **日期显示**: 年-月-日格式 (20YY-MM-DD)
- **星期显示**: 英文缩写 (Mon, <PERSON><PERSON>, <PERSON>d, Thu, Fri, Sat, Sun)
- **自动日期更新**: 支持闰年计算和月份天数自动调整
- **装饰界面**: 带边框装饰和秒数指示器闪烁效果
- **启动画面**: 显示"Digital Watch"启动信息

## 硬件要求
- STM32F407VET6开发板
- 0.96寸IIC OLED显示屏 (SSD1306)
- IIC连接线

## 连接方式
| OLED引脚 | STM32引脚 | 功能 |
|----------|-----------|------|
| VCC      | 3.3V      | 电源 |
| GND      | GND       | 地线 |
| SCL      | PB10      | IIC时钟线 |
| SDA      | PB11      | IIC数据线 |

## 软件架构
- **软件时钟**: 使用软件计时器实现时间计算，避免硬件RTC依赖
- **闰年支持**: 自动识别闰年并调整2月天数
- **月份管理**: 自动处理不同月份的天数差异
- **星期计算**: 自动更新星期信息

## 显示布局
```
=================
      *          <- 秒数指示器(闪烁)
   HH:MM:SS      <- 时间显示(16号字体)
 20YY-MM-DD Wed  <- 日期和星期(12号字体)
=================
```

## 初始设置
程序默认设置为：
- 时间: 23:59:50
- 日期: 2024年12月31日
- 星期: 星期二

可在代码中修改 `SoftClock_t clock` 初始值来设置不同的起始时间。

## 编译和烧录
1. 使用Keil MDK-ARM打开项目文件
2. 编译项目
3. 通过ST-Link或其他调试器烧录到开发板

## 代码优化特点
- **最少行数实现**: 核心功能控制在最少代码行数
- **右侧注释**: 所有注释采用行尾注释格式
- **中文友好**: 支持中文注释和说明
- **性能优化**: 每秒更新一次，减少不必要的刷新

## 使用说明
1. 上电后显示启动画面2秒
2. 自动进入手表界面
3. 时间每秒自动更新
4. 右上角星号每秒闪烁一次作为秒数指示器
5. 日期和星期自动跟随时间更新

## 扩展功能建议
- 添加按键设置时间功能
- 添加闹钟功能
- 添加温度显示
- 添加不同的显示主题
- 添加省电模式

## 注意事项
- 本程序使用软件时钟，断电后时间会重置
- 如需保持时间，建议添加硬件RTC支持
- OLED显示屏需要正确连接IIC引脚
- 确保电源供应稳定