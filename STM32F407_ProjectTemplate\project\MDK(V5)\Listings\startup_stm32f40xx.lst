


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2016 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f40xx.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* @version           : V1.8.1
    5 00000000         ;* @date              : 27-January-2022
    6 00000000         ;* Description        : STM32F40xxx/41xxx devices vector
                        table for MDK-ARM toolchain. 
    7 00000000         ;*                      Same as startup_stm32f40_41xxx.s
                        and maintained for legacy purpose 
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Configure the system clock and
                        the external SRAM mounted on 
   13 00000000         ;*                        STM324xG-EVAL board to be used
                        as data memory (optional, 
   14 00000000         ;*                        to be enabled by user)
   15 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   16 00000000         ;*                        calls main()).
   17 00000000         ;*                      After Reset the CortexM4 process
                       or is in Thread mode,
   18 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   19 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   20 00000000         ;*******************************************************
                       ***********************
   21 00000000         ;* @attention
   22 00000000         ;*
   23 00000000         ;* Copyright (c) 2016 STMicroelectronics.
   24 00000000         ;* All rights reserved.
   25 00000000         ;*
   26 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   27 00000000         ;* in the root directory of this software component.
   28 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   29 00000000         ;*
   30 00000000         ;*******************************************************
                       ***********************
   31 00000000         
   32 00000000         ; Amount of memory (in bytes) allocated for Stack
   33 00000000         ; Tailor this value to your application needs
   34 00000000         ; <h> Stack Configuration
   35 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   36 00000000         ; </h>
   37 00000000         
   38 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   39 00000000         
   40 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   41 00000000         Stack_Mem
                               SPACE            Stack_Size



ARM Macro Assembler    Page 2 


   42 00000400         __initial_sp
   43 00000400         
   44 00000400         
   45 00000400         ; <h> Heap Configuration
   46 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   47 00000400         ; </h>
   48 00000400         
   49 00000400 00000200 
                       Heap_Size
                               EQU              0x00000200
   50 00000400         
   51 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   52 00000000         __heap_base
   53 00000000         Heap_Mem
                               SPACE            Heap_Size
   54 00000200         __heap_limit
   55 00000200         
   56 00000200                 PRESERVE8
   57 00000200                 THUMB
   58 00000200         
   59 00000200         
   60 00000200         ; Vector Table Mapped to Address 0 at Reset
   61 00000200                 AREA             RESET, DATA, READONLY
   62 00000000                 EXPORT           __Vectors
   63 00000000                 EXPORT           __Vectors_End
   64 00000000                 EXPORT           __Vectors_Size
   65 00000000         
   66 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   67 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   68 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   69 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   70 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   71 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   72 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   73 0000001C 00000000        DCD              0           ; Reserved
   74 00000020 00000000        DCD              0           ; Reserved
   75 00000024 00000000        DCD              0           ; Reserved
   76 00000028 00000000        DCD              0           ; Reserved
   77 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   78 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   79 00000034 00000000        DCD              0           ; Reserved
   80 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   81 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   82 00000040         
   83 00000040         ; External Interrupts
   84 00000040 00000000        DCD              WWDG_IRQHandler ; Window WatchD
                                                            og                 



ARM Macro Assembler    Page 3 


                                                                               
                                                                
   85 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detection  
                                                                               
                                                               
   86 00000048 00000000        DCD              TAMP_STAMP_IRQHandler ; Tamper 
                                                            and TimeStamps thro
                                                            ugh the EXTI line  
                                                                      
   87 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line               
                                                                    
   88 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH       
                                                                               
                                                                             
   89 00000054 00000000        DCD              RCC_IRQHandler ; RCC           
                                                                               
                                                                           
   90 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0  
                                                                               
                                                                               
                                                                 
   91 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1  
                                                                               
                                                                               
                                                                 
   92 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2  
                                                                               
                                                                               
                                                                 
   93 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3  
                                                                               
                                                                               
                                                                 
   94 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4  
                                                                               
                                                                               
                                                                 
   95 0000006C 00000000        DCD              DMA1_Stream0_IRQHandler ; DMA1 
                                                            Stream 0           
                                                                               
                                                                 
   96 00000070 00000000        DCD              DMA1_Stream1_IRQHandler ; DMA1 
                                                            Stream 1           
                                                                               
                                                                 
   97 00000074 00000000        DCD              DMA1_Stream2_IRQHandler ; DMA1 
                                                            Stream 2           
                                                                               
                                                                 
   98 00000078 00000000        DCD              DMA1_Stream3_IRQHandler ; DMA1 
                                                            Stream 3           
                                                                               
                                                                 
   99 0000007C 00000000        DCD              DMA1_Stream4_IRQHandler ; DMA1 
                                                            Stream 4           
                                                                               



ARM Macro Assembler    Page 4 


                                                                 
  100 00000080 00000000        DCD              DMA1_Stream5_IRQHandler ; DMA1 
                                                            Stream 5           
                                                                               
                                                                 
  101 00000084 00000000        DCD              DMA1_Stream6_IRQHandler ; DMA1 
                                                            Stream 6           
                                                                               
                                                                 
  102 00000088 00000000        DCD              ADC_IRQHandler ; ADC1, ADC2 and
                                                             ADC3s             
                                                                           
  103 0000008C 00000000        DCD              CAN1_TX_IRQHandler ; CAN1 TX   
                                                                               
                                                                               
                                                                   
  104 00000090 00000000        DCD              CAN1_RX0_IRQHandler ; CAN1 RX0 
                                                                               
                                                                               
                                                                    
  105 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1 
                                                                               
                                                                               
                                                                    
  106 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE 
                                                                               
                                                                               
                                                                    
  107 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s          
                                                                               
                                                                   
  108 000000A0 00000000        DCD              TIM1_BRK_TIM9_IRQHandler ; TIM1
                                                             Break and TIM9    
                                                                           
  109 000000A4 00000000        DCD              TIM1_UP_TIM10_IRQHandler ; TIM1
                                                             Update and TIM10  
                                                                           
  110 000000A8 00000000        DCD              TIM1_TRG_COM_TIM11_IRQHandler ;
                                                             TIM1 Trigger and C
                                                            ommutation and TIM1
                                                            1
  111 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare         
                                                                               
                                                                   
  112 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2         
                                                                               
                                                                            
  113 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3         
                                                                               
                                                                            
  114 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4         
                                                                               
                                                                            
  115 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                                               
                                                                               
                                                                   



ARM Macro Assembler    Page 5 


  116 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                                               
                                                                               
                                                                   
  117 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                                               
                                                                               
                                                                   
  118 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                                               
                                                                               
                                                                     
  119 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1         
                                                                               
                                                                            
  120 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2         
                                                                               
                                                                            
  121 000000D4 00000000        DCD              USART1_IRQHandler ; USART1     
                                                                               
                                                                              
  122 000000D8 00000000        DCD              USART2_IRQHandler ; USART2     
                                                                               
                                                                              
  123 000000DC 00000000        DCD              USART3_IRQHandler ; USART3     
                                                                               
                                                                              
  124 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]s      
                                                                               
                                                                     
  125 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line         
                                                                     
  126 000000E8 00000000        DCD              OTG_FS_WKUP_IRQHandler ; USB OT
                                                            G FS Wakeup through
                                                             EXTI line         
                                                                           
  127 000000EC 00000000        DCD              TIM8_BRK_TIM12_IRQHandler ; TIM
                                                            8 Break and TIM12  
                                                                            
  128 000000F0 00000000        DCD              TIM8_UP_TIM13_IRQHandler ; TIM8
                                                             Update and TIM13  
                                                                           
  129 000000F4 00000000        DCD              TIM8_TRG_COM_TIM14_IRQHandler ;
                                                             TIM8 Trigger and C
                                                            ommutation and TIM1
                                                            4
  130 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare         
                                                                               
                                                                   
  131 000000FC 00000000        DCD              DMA1_Stream7_IRQHandler ; DMA1 
                                                            Stream7            
                                                                               
                                                                        
  132 00000100 00000000        DCD              FSMC_IRQHandler ; FSMC         
                                                                               



ARM Macro Assembler    Page 6 


                                                                            
  133 00000104 00000000        DCD              SDIO_IRQHandler ; SDIO         
                                                                               
                                                                            
  134 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5         
                                                                               
                                                                            
  135 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3         
                                                                               
                                                                            
  136 00000110 00000000        DCD              UART4_IRQHandler ; UART4       
                                                                               
                                                                             
  137 00000114 00000000        DCD              UART5_IRQHandler ; UART5       
                                                                               
                                                                             
  138 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 
                                                            DAC1&2 underrun err
                                                            ors                
                                                               
  139 0000011C 00000000        DCD              TIM7_IRQHandler ; TIM7         
                                                                      
  140 00000120 00000000        DCD              DMA2_Stream0_IRQHandler ; DMA2 
                                                            Stream 0           
                                                                               
                                                                 
  141 00000124 00000000        DCD              DMA2_Stream1_IRQHandler ; DMA2 
                                                            Stream 1           
                                                                               
                                                                 
  142 00000128 00000000        DCD              DMA2_Stream2_IRQHandler ; DMA2 
                                                            Stream 2           
                                                                               
                                                                 
  143 0000012C 00000000        DCD              DMA2_Stream3_IRQHandler ; DMA2 
                                                            Stream 3           
                                                                               
                                                                 
  144 00000130 00000000        DCD              DMA2_Stream4_IRQHandler ; DMA2 
                                                            Stream 4           
                                                                               
                                                                 
  145 00000134 00000000        DCD              ETH_IRQHandler ; Ethernet      
                                                                               
                                                                           
  146 00000138 00000000        DCD              ETH_WKUP_IRQHandler ; Ethernet 
                                                            Wakeup through EXTI
                                                             line              
                                                                    
  147 0000013C 00000000        DCD              CAN2_TX_IRQHandler ; CAN2 TX   
                                                                               
                                                                               
                                                                   
  148 00000140 00000000        DCD              CAN2_RX0_IRQHandler ; CAN2 RX0 
                                                                               
                                                                               
                                                                    
  149 00000144 00000000        DCD              CAN2_RX1_IRQHandler ; CAN2 RX1 
                                                                               



ARM Macro Assembler    Page 7 


                                                                               
                                                                    
  150 00000148 00000000        DCD              CAN2_SCE_IRQHandler ; CAN2 SCE 
                                                                               
                                                                               
                                                                    
  151 0000014C 00000000        DCD              OTG_FS_IRQHandler ; USB OTG FS 
                                                                               
                                                                              
  152 00000150 00000000        DCD              DMA2_Stream5_IRQHandler ; DMA2 
                                                            Stream 5           
                                                                               
                                                                 
  153 00000154 00000000        DCD              DMA2_Stream6_IRQHandler ; DMA2 
                                                            Stream 6           
                                                                               
                                                                 
  154 00000158 00000000        DCD              DMA2_Stream7_IRQHandler ; DMA2 
                                                            Stream 7           
                                                                               
                                                                 
  155 0000015C 00000000        DCD              USART6_IRQHandler ; USART6     
                                                                               
                                                                               
                                                            
  156 00000160 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 event
                                                                               
                                                                               
                                                                   
  157 00000164 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 error
                                                                               
                                                                               
                                                                   
  158 00000168 00000000        DCD              OTG_HS_EP1_OUT_IRQHandler ; USB
                                                             OTG HS End Point 1
                                                             Out               
                                                                   
  159 0000016C 00000000        DCD              OTG_HS_EP1_IN_IRQHandler ; USB 
                                                            OTG HS End Point 1 
                                                            In                 
                                                                  
  160 00000170 00000000        DCD              OTG_HS_WKUP_IRQHandler ; USB OT
                                                            G HS Wakeup through
                                                             EXTI              
                                                                       
  161 00000174 00000000        DCD              OTG_HS_IRQHandler ; USB OTG HS 
                                                                               
                                                                              
  162 00000178 00000000        DCD              DCMI_IRQHandler ; DCMI         
                                                                               
                                                                            
  163 0000017C 00000000        DCD              CRYP_IRQHandler ; CRYP crypto  
                                                                               
                                                                            
  164 00000180 00000000        DCD              HASH_RNG_IRQHandler 
                                                            ; Hash and Rng
  165 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  166 00000188         
  167 00000188         __Vectors_End



ARM Macro Assembler    Page 8 


  168 00000188         
  169 00000188 00000188 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  170 00000188         
  171 00000188                 AREA             |.text|, CODE, READONLY
  172 00000000         
  173 00000000         ; Reset handler
  174 00000000         Reset_Handler
                               PROC
  175 00000000                 EXPORT           Reset_Handler             [WEAK
]
  176 00000000                 IMPORT           SystemInit
  177 00000000                 IMPORT           __main
  178 00000000         
  179 00000000 4806            LDR              R0, =SystemInit
  180 00000002 4780            BLX              R0
  181 00000004 4806            LDR              R0, =__main
  182 00000006 4700            BX               R0
  183 00000008                 ENDP
  184 00000008         
  185 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  186 00000008         
  187 00000008         NMI_Handler
                               PROC
  188 00000008                 EXPORT           NMI_Handler                [WEA
K]
  189 00000008 E7FE            B                .
  190 0000000A                 ENDP
  192 0000000A         HardFault_Handler
                               PROC
  193 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  194 0000000A E7FE            B                .
  195 0000000C                 ENDP
  197 0000000C         MemManage_Handler
                               PROC
  198 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  199 0000000C E7FE            B                .
  200 0000000E                 ENDP
  202 0000000E         BusFault_Handler
                               PROC
  203 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  204 0000000E E7FE            B                .
  205 00000010                 ENDP
  207 00000010         UsageFault_Handler
                               PROC
  208 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  209 00000010 E7FE            B                .
  210 00000012                 ENDP
  211 00000012         SVC_Handler
                               PROC
  212 00000012                 EXPORT           SVC_Handler                [WEA
K]
  213 00000012 E7FE            B                .



ARM Macro Assembler    Page 9 


  214 00000014                 ENDP
  216 00000014         DebugMon_Handler
                               PROC
  217 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  218 00000014 E7FE            B                .
  219 00000016                 ENDP
  220 00000016         PendSV_Handler
                               PROC
  221 00000016                 EXPORT           PendSV_Handler             [WEA
K]
  222 00000016 E7FE            B                .
  223 00000018                 ENDP
  224 00000018         SysTick_Handler
                               PROC
  225 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  226 00000018 E7FE            B                .
  227 0000001A                 ENDP
  228 0000001A         
  229 0000001A         Default_Handler
                               PROC
  230 0000001A         
  231 0000001A                 EXPORT           WWDG_IRQHandler                
   [WEAK]
  232 0000001A                 EXPORT           PVD_IRQHandler                 
   [WEAK]
  233 0000001A                 EXPORT           TAMP_STAMP_IRQHandler          
   [WEAK]
  234 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  235 0000001A                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  236 0000001A                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  237 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  238 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  239 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  240 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  241 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  242 0000001A                 EXPORT           DMA1_Stream0_IRQHandler        
   [WEAK]
  243 0000001A                 EXPORT           DMA1_Stream1_IRQHandler        
   [WEAK]
  244 0000001A                 EXPORT           DMA1_Stream2_IRQHandler        
   [WEAK]
  245 0000001A                 EXPORT           DMA1_Stream3_IRQHandler        
   [WEAK]
  246 0000001A                 EXPORT           DMA1_Stream4_IRQHandler        
   [WEAK]
  247 0000001A                 EXPORT           DMA1_Stream5_IRQHandler        
   [WEAK]
  248 0000001A                 EXPORT           DMA1_Stream6_IRQHandler        
   [WEAK]



ARM Macro Assembler    Page 10 


  249 0000001A                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  250 0000001A                 EXPORT           CAN1_TX_IRQHandler             
   [WEAK]
  251 0000001A                 EXPORT           CAN1_RX0_IRQHandler            
   [WEAK]
  252 0000001A                 EXPORT           CAN1_RX1_IRQHandler            
   [WEAK]
  253 0000001A                 EXPORT           CAN1_SCE_IRQHandler            
   [WEAK]
  254 0000001A                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  255 0000001A                 EXPORT           TIM1_BRK_TIM9_IRQHandler       
   [WEAK]
  256 0000001A                 EXPORT           TIM1_UP_TIM10_IRQHandler       
   [WEAK]
  257 0000001A                 EXPORT           TIM1_TRG_COM_TIM11_IRQHandler  
   [WEAK]
  258 0000001A                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  259 0000001A                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  260 0000001A                 EXPORT           TIM3_IRQHandler                
   [WEAK]
  261 0000001A                 EXPORT           TIM4_IRQHandler                
   [WEAK]
  262 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  263 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  264 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  265 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  266 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  267 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  268 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  269 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  270 0000001A                 EXPORT           USART3_IRQHandler              
   [WEAK]
  271 0000001A                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  272 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  273 0000001A                 EXPORT           OTG_FS_WKUP_IRQHandler         
   [WEAK]
  274 0000001A                 EXPORT           TIM8_BRK_TIM12_IRQHandler      
   [WEAK]
  275 0000001A                 EXPORT           TIM8_UP_TIM13_IRQHandler       
   [WEAK]
  276 0000001A                 EXPORT           TIM8_TRG_COM_TIM14_IRQHandler  
   [WEAK]
  277 0000001A                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  278 0000001A                 EXPORT           DMA1_Stream7_IRQHandler        



ARM Macro Assembler    Page 11 


   [WEAK]
  279 0000001A                 EXPORT           FSMC_IRQHandler                
   [WEAK]
  280 0000001A                 EXPORT           SDIO_IRQHandler                
   [WEAK]
  281 0000001A                 EXPORT           TIM5_IRQHandler                
   [WEAK]
  282 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  283 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  284 0000001A                 EXPORT           UART5_IRQHandler               
   [WEAK]
  285 0000001A                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  286 0000001A                 EXPORT           TIM7_IRQHandler                
   [WEAK]
  287 0000001A                 EXPORT           DMA2_Stream0_IRQHandler        
   [WEAK]
  288 0000001A                 EXPORT           DMA2_Stream1_IRQHandler        
   [WEAK]
  289 0000001A                 EXPORT           DMA2_Stream2_IRQHandler        
   [WEAK]
  290 0000001A                 EXPORT           DMA2_Stream3_IRQHandler        
   [WEAK]
  291 0000001A                 EXPORT           DMA2_Stream4_IRQHandler        
   [WEAK]
  292 0000001A                 EXPORT           ETH_IRQHandler                 
   [WEAK]
  293 0000001A                 EXPORT           ETH_WKUP_IRQHandler            
   [WEAK]
  294 0000001A                 EXPORT           CAN2_TX_IRQHandler             
   [WEAK]
  295 0000001A                 EXPORT           CAN2_RX0_IRQHandler            
   [WEAK]
  296 0000001A                 EXPORT           CAN2_RX1_IRQHandler            
   [WEAK]
  297 0000001A                 EXPORT           CAN2_SCE_IRQHandler            
   [WEAK]
  298 0000001A                 EXPORT           OTG_FS_IRQHandler              
   [WEAK]
  299 0000001A                 EXPORT           DMA2_Stream5_IRQHandler        
   [WEAK]
  300 0000001A                 EXPORT           DMA2_Stream6_IRQHandler        
   [WEAK]
  301 0000001A                 EXPORT           DMA2_Stream7_IRQHandler        
   [WEAK]
  302 0000001A                 EXPORT           USART6_IRQHandler              
   [WEAK]
  303 0000001A                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  304 0000001A                 EXPORT           I2C3_ER_IRQHandler             
   [WEAK]
  305 0000001A                 EXPORT           OTG_HS_EP1_OUT_IRQHandler      
   [WEAK]
  306 0000001A                 EXPORT           OTG_HS_EP1_IN_IRQHandler       
   [WEAK]
  307 0000001A                 EXPORT           OTG_HS_WKUP_IRQHandler         
   [WEAK]



ARM Macro Assembler    Page 12 


  308 0000001A                 EXPORT           OTG_HS_IRQHandler              
   [WEAK]
  309 0000001A                 EXPORT           DCMI_IRQHandler                
   [WEAK]
  310 0000001A                 EXPORT           CRYP_IRQHandler                
   [WEAK]
  311 0000001A                 EXPORT           HASH_RNG_IRQHandler            
   [WEAK]
  312 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  313 0000001A         
  314 0000001A         WWDG_IRQHandler
  315 0000001A         PVD_IRQHandler
  316 0000001A         TAMP_STAMP_IRQHandler
  317 0000001A         RTC_WKUP_IRQHandler
  318 0000001A         FLASH_IRQHandler
  319 0000001A         RCC_IRQHandler
  320 0000001A         EXTI0_IRQHandler
  321 0000001A         EXTI1_IRQHandler
  322 0000001A         EXTI2_IRQHandler
  323 0000001A         EXTI3_IRQHandler
  324 0000001A         EXTI4_IRQHandler
  325 0000001A         DMA1_Stream0_IRQHandler
  326 0000001A         DMA1_Stream1_IRQHandler
  327 0000001A         DMA1_Stream2_IRQHandler
  328 0000001A         DMA1_Stream3_IRQHandler
  329 0000001A         DMA1_Stream4_IRQHandler
  330 0000001A         DMA1_Stream5_IRQHandler
  331 0000001A         DMA1_Stream6_IRQHandler
  332 0000001A         ADC_IRQHandler
  333 0000001A         CAN1_TX_IRQHandler
  334 0000001A         CAN1_RX0_IRQHandler
  335 0000001A         CAN1_RX1_IRQHandler
  336 0000001A         CAN1_SCE_IRQHandler
  337 0000001A         EXTI9_5_IRQHandler
  338 0000001A         TIM1_BRK_TIM9_IRQHandler
  339 0000001A         TIM1_UP_TIM10_IRQHandler
  340 0000001A         TIM1_TRG_COM_TIM11_IRQHandler
  341 0000001A         TIM1_CC_IRQHandler
  342 0000001A         TIM2_IRQHandler
  343 0000001A         TIM3_IRQHandler
  344 0000001A         TIM4_IRQHandler
  345 0000001A         I2C1_EV_IRQHandler
  346 0000001A         I2C1_ER_IRQHandler
  347 0000001A         I2C2_EV_IRQHandler
  348 0000001A         I2C2_ER_IRQHandler
  349 0000001A         SPI1_IRQHandler
  350 0000001A         SPI2_IRQHandler
  351 0000001A         USART1_IRQHandler
  352 0000001A         USART2_IRQHandler
  353 0000001A         USART3_IRQHandler
  354 0000001A         EXTI15_10_IRQHandler
  355 0000001A         RTC_Alarm_IRQHandler
  356 0000001A         OTG_FS_WKUP_IRQHandler
  357 0000001A         TIM8_BRK_TIM12_IRQHandler
  358 0000001A         TIM8_UP_TIM13_IRQHandler
  359 0000001A         TIM8_TRG_COM_TIM14_IRQHandler
  360 0000001A         TIM8_CC_IRQHandler
  361 0000001A         DMA1_Stream7_IRQHandler



ARM Macro Assembler    Page 13 


  362 0000001A         FSMC_IRQHandler
  363 0000001A         SDIO_IRQHandler
  364 0000001A         TIM5_IRQHandler
  365 0000001A         SPI3_IRQHandler
  366 0000001A         UART4_IRQHandler
  367 0000001A         UART5_IRQHandler
  368 0000001A         TIM6_DAC_IRQHandler
  369 0000001A         TIM7_IRQHandler
  370 0000001A         DMA2_Stream0_IRQHandler
  371 0000001A         DMA2_Stream1_IRQHandler
  372 0000001A         DMA2_Stream2_IRQHandler
  373 0000001A         DMA2_Stream3_IRQHandler
  374 0000001A         DMA2_Stream4_IRQHandler
  375 0000001A         ETH_IRQHandler
  376 0000001A         ETH_WKUP_IRQHandler
  377 0000001A         CAN2_TX_IRQHandler
  378 0000001A         CAN2_RX0_IRQHandler
  379 0000001A         CAN2_RX1_IRQHandler
  380 0000001A         CAN2_SCE_IRQHandler
  381 0000001A         OTG_FS_IRQHandler
  382 0000001A         DMA2_Stream5_IRQHandler
  383 0000001A         DMA2_Stream6_IRQHandler
  384 0000001A         DMA2_Stream7_IRQHandler
  385 0000001A         USART6_IRQHandler
  386 0000001A         I2C3_EV_IRQHandler
  387 0000001A         I2C3_ER_IRQHandler
  388 0000001A         OTG_HS_EP1_OUT_IRQHandler
  389 0000001A         OTG_HS_EP1_IN_IRQHandler
  390 0000001A         OTG_HS_WKUP_IRQHandler
  391 0000001A         OTG_HS_IRQHandler
  392 0000001A         DCMI_IRQHandler
  393 0000001A         CRYP_IRQHandler
  394 0000001A         HASH_RNG_IRQHandler
  395 0000001A         FPU_IRQHandler
  396 0000001A         
  397 0000001A E7FE            B                .
  398 0000001C         
  399 0000001C                 ENDP
  400 0000001C         
  401 0000001C                 ALIGN
  402 0000001C         
  403 0000001C         ;*******************************************************
                       ************************
  404 0000001C         ; User Stack and Heap initialization
  405 0000001C         ;*******************************************************
                       ************************
  406 0000001C                 IF               :DEF:__MICROLIB
  407 0000001C         
  408 0000001C                 EXPORT           __initial_sp
  409 0000001C                 EXPORT           __heap_base
  410 0000001C                 EXPORT           __heap_limit
  411 0000001C         
  412 0000001C                 ELSE
  427                          ENDIF
  428 0000001C         
  429 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=



ARM Macro Assembler    Page 14 


interwork --depend=.\objects\startup_stm32f40xx.d -o.\objects\startup_stm32f40x
x.o -ID:\keilarm\ARM\PACK\Keil\STM32F4xx_DFP\2.17.1\Drivers\CMSIS\Device\ST\STM
32F4xx\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION S
ETA 542" --predefine="STM32F407xx SETA 1" --list=.\listings\startup_stm32f40xx.
lst ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm3
2f40xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 40 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 41 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 42 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 66 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 408 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 51 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 53 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 52 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 409 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
Comment: __heap_base used once
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 54 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 410 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 61 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 66 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 62 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 169 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

__Vectors_End 00000188

Symbol: __Vectors_End
   Definitions
      At line 167 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 63 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 169 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 171 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 0000001A

Symbol: ADC_IRQHandler
   Definitions
      At line 332 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 102 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 249 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 202 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 71 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 203 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN1_RX0_IRQHandler 0000001A

Symbol: CAN1_RX0_IRQHandler
   Definitions
      At line 334 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 104 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 251 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 335 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 105 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 252 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN1_SCE_IRQHandler 0000001A




ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 336 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 106 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 253 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN1_TX_IRQHandler 0000001A

Symbol: CAN1_TX_IRQHandler
   Definitions
      At line 333 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 103 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 250 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN2_RX0_IRQHandler 0000001A

Symbol: CAN2_RX0_IRQHandler
   Definitions
      At line 378 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 148 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 295 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN2_RX1_IRQHandler 0000001A

Symbol: CAN2_RX1_IRQHandler
   Definitions
      At line 379 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 149 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 296 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN2_SCE_IRQHandler 0000001A

Symbol: CAN2_SCE_IRQHandler
   Definitions
      At line 380 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 150 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 297 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CAN2_TX_IRQHandler 0000001A



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols


Symbol: CAN2_TX_IRQHandler
   Definitions
      At line 377 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 147 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 294 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

CRYP_IRQHandler 0000001A

Symbol: CRYP_IRQHandler
   Definitions
      At line 393 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 163 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 310 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DCMI_IRQHandler 0000001A

Symbol: DCMI_IRQHandler
   Definitions
      At line 392 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 162 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 309 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream0_IRQHandler 0000001A

Symbol: DMA1_Stream0_IRQHandler
   Definitions
      At line 325 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 95 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 242 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream1_IRQHandler 0000001A

Symbol: DMA1_Stream1_IRQHandler
   Definitions
      At line 326 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 96 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 243 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s




ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

DMA1_Stream2_IRQHandler 0000001A

Symbol: DMA1_Stream2_IRQHandler
   Definitions
      At line 327 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 97 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 244 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream3_IRQHandler 0000001A

Symbol: DMA1_Stream3_IRQHandler
   Definitions
      At line 328 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 98 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 245 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream4_IRQHandler 0000001A

Symbol: DMA1_Stream4_IRQHandler
   Definitions
      At line 329 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 99 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 246 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream5_IRQHandler 0000001A

Symbol: DMA1_Stream5_IRQHandler
   Definitions
      At line 330 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 100 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 247 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA1_Stream6_IRQHandler 0000001A

Symbol: DMA1_Stream6_IRQHandler
   Definitions
      At line 331 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 101 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 248 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols


DMA1_Stream7_IRQHandler 0000001A

Symbol: DMA1_Stream7_IRQHandler
   Definitions
      At line 361 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 131 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 278 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream0_IRQHandler 0000001A

Symbol: DMA2_Stream0_IRQHandler
   Definitions
      At line 370 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 140 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 287 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream1_IRQHandler 0000001A

Symbol: DMA2_Stream1_IRQHandler
   Definitions
      At line 371 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 141 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 288 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream2_IRQHandler 0000001A

Symbol: DMA2_Stream2_IRQHandler
   Definitions
      At line 372 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 142 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 289 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream3_IRQHandler 0000001A

Symbol: DMA2_Stream3_IRQHandler
   Definitions
      At line 373 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 143 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 290 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

lates\arm\startup_stm32f40xx.s

DMA2_Stream4_IRQHandler 0000001A

Symbol: DMA2_Stream4_IRQHandler
   Definitions
      At line 374 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 144 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 291 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream5_IRQHandler 0000001A

Symbol: DMA2_Stream5_IRQHandler
   Definitions
      At line 382 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 152 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 299 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream6_IRQHandler 0000001A

Symbol: DMA2_Stream6_IRQHandler
   Definitions
      At line 383 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 153 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 300 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DMA2_Stream7_IRQHandler 0000001A

Symbol: DMA2_Stream7_IRQHandler
   Definitions
      At line 384 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 154 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 301 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 216 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 78 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 217 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 229 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      None
Comment: Default_Handler unused
ETH_IRQHandler 0000001A

Symbol: ETH_IRQHandler
   Definitions
      At line 375 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 145 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 292 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

ETH_WKUP_IRQHandler 0000001A

Symbol: ETH_WKUP_IRQHandler
   Definitions
      At line 376 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 146 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 293 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 320 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 90 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 237 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 354 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 124 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 271 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 321 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 91 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 238 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 322 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 92 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 239 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 323 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 93 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 240 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 324 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 94 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 241 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 337 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 107 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 254 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

lates\arm\startup_stm32f40xx.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 318 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 88 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 235 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 395 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 165 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 312 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

FSMC_IRQHandler 0000001A

Symbol: FSMC_IRQHandler
   Definitions
      At line 362 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 132 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 279 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

HASH_RNG_IRQHandler 0000001A

Symbol: HASH_RNG_IRQHandler
   Definitions
      At line 394 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 164 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 311 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 192 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 69 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

      At line 193 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 346 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 116 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 263 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 345 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 115 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 262 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 348 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 118 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 265 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 347 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 117 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 264 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C3_ER_IRQHandler 0000001A

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 387 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 157 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

lates\arm\startup_stm32f40xx.s
      At line 304 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

I2C3_EV_IRQHandler 0000001A

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 386 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 156 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 303 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 197 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 70 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 198 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 187 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 68 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 188 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_FS_IRQHandler 0000001A

Symbol: OTG_FS_IRQHandler
   Definitions
      At line 381 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 151 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 298 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_FS_WKUP_IRQHandler 0000001A

Symbol: OTG_FS_WKUP_IRQHandler
   Definitions
      At line 356 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

      At line 126 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 273 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_HS_EP1_IN_IRQHandler 0000001A

Symbol: OTG_HS_EP1_IN_IRQHandler
   Definitions
      At line 389 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 159 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 306 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_HS_EP1_OUT_IRQHandler 0000001A

Symbol: OTG_HS_EP1_OUT_IRQHandler
   Definitions
      At line 388 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 158 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 305 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_HS_IRQHandler 0000001A

Symbol: OTG_HS_IRQHandler
   Definitions
      At line 391 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 161 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 308 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

OTG_HS_WKUP_IRQHandler 0000001A

Symbol: OTG_HS_WKUP_IRQHandler
   Definitions
      At line 390 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 160 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 307 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

PVD_IRQHandler 0000001A

Symbol: PVD_IRQHandler
   Definitions
      At line 315 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 85 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 232 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 220 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 80 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 221 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 319 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 89 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 236 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 355 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 125 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 272 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 317 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 87 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 234 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 174 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

lates\arm\startup_stm32f40xx.s
   Uses
      At line 67 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 175 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SDIO_IRQHandler 0000001A

Symbol: SDIO_IRQHandler
   Definitions
      At line 363 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 133 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 280 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 349 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 119 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 266 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 350 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 120 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 267 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 365 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 135 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 282 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols

      At line 211 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 77 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 212 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 224 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 81 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 225 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TAMP_STAMP_IRQHandler 0000001A

Symbol: TAMP_STAMP_IRQHandler
   Definitions
      At line 316 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 86 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 233 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM1_BRK_TIM9_IRQHandler 0000001A

Symbol: TIM1_BRK_TIM9_IRQHandler
   Definitions
      At line 338 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 108 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 255 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 341 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 111 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 258 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM1_TRG_COM_TIM11_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_TIM11_IRQHandler



ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 340 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 110 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 257 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM1_UP_TIM10_IRQHandler 0000001A

Symbol: TIM1_UP_TIM10_IRQHandler
   Definitions
      At line 339 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 109 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 256 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 342 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 112 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 259 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 343 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 113 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 260 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 344 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 114 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 261 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM5_IRQHandler 0000001A




ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols

Symbol: TIM5_IRQHandler
   Definitions
      At line 364 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 134 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 281 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM6_DAC_IRQHandler 0000001A

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 368 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 138 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 285 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM7_IRQHandler 0000001A

Symbol: TIM7_IRQHandler
   Definitions
      At line 369 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 139 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 286 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM8_BRK_TIM12_IRQHandler 0000001A

Symbol: TIM8_BRK_TIM12_IRQHandler
   Definitions
      At line 357 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 127 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 274 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 360 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 130 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 277 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM8_TRG_COM_TIM14_IRQHandler 0000001A



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM8_TRG_COM_TIM14_IRQHandler
   Definitions
      At line 359 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 129 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 276 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

TIM8_UP_TIM13_IRQHandler 0000001A

Symbol: TIM8_UP_TIM13_IRQHandler
   Definitions
      At line 358 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 128 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 275 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 366 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 136 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 283 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 367 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 137 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 284 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 351 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 121 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 268 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s




ARM Macro Assembler    Page 19 Alphabetic symbol ordering
Relocatable symbols

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 352 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 122 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 269 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 353 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 123 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 270 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

USART6_IRQHandler 0000001A

Symbol: USART6_IRQHandler
   Definitions
      At line 385 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 155 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
      At line 302 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 207 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 72 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 208 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 314 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 84 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
      At line 231 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s



ARM Macro Assembler    Page 20 Alphabetic symbol ordering
Relocatable symbols


94 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 49 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 53 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 38 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
   Uses
      At line 41 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
Comment: Stack_Size used once
__Vectors_Size 00000188

Symbol: __Vectors_Size
   Definitions
      At line 169 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 64 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templ
ates\arm\startup_stm32f40xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 176 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 179 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 177 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
   Uses
      At line 181 in file ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Temp
lates\arm\startup_stm32f40xx.s
Comment: __main used once
2 symbols
445 symbols in table
