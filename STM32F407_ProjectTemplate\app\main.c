/*
 * ������������Ӳ�������������չ����Ӳ�����Ϲ���ȫ����Դ
 * �����������www.lckfb.com
 * ����֧�ֳ�פ��̳���κμ������⻶ӭ��ʱ����ѧϰ
 * ������̳��https://oshwhub.com/forum
 * ��עbilibili�˺ţ������������塿���������ǵ����¶�̬��
 * ��������׬Ǯ���������й�����ʦΪ����
 * 

 Change Logs:
 * Date           Author       Notes
 * 2024-03-14     LCKFB-LP    first version
 */
#include "board.h"
#include "bsp_uart.h"
#include <stdio.h>
#include "oled.h"

// 星期字符串数组
const char* weekdays[] = {"", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sat", "Sun"};

// 软件时钟结构体
typedef struct {
    uint8_t hours;
    uint8_t minutes;
    uint8_t seconds;
    uint8_t year;    // 从2000年开始
    uint8_t month;   // 1-12
    uint8_t date;    // 1-31
    uint8_t weekday; // 1-7 (1=Monday)
} SoftClock_t;

SoftClock_t clock = {13, 41, 50, 25, 8, 11, 1}; // 2024年12月31日23:59:50 星期二

// 判断是否为闰年
uint8_t IsLeapYear(uint16_t year) {
    return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0));
}

// 获取每月天数
uint8_t GetDaysInMonth(uint8_t month, uint16_t year) {
    const uint8_t days[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if (month == 2 && IsLeapYear(year)) return 29;
    return days[month];
}

// 更新时钟
void UpdateClock(void) {
    clock.seconds++;
    if (clock.seconds >= 60) {
        clock.seconds = 0;
        clock.minutes++;
        if (clock.minutes >= 60) {
            clock.minutes = 0;
            clock.hours++;
            if (clock.hours >= 24) {
                clock.hours = 0;
                clock.date++;
                clock.weekday++;
                if (clock.weekday > 7) clock.weekday = 1;

                uint8_t days_in_month = GetDaysInMonth(clock.month, 2000 + clock.year);
                if (clock.date > days_in_month) {
                    clock.date = 1;
                    clock.month++;
                    if (clock.month > 12) {
                        clock.month = 1;
                        clock.year++;
                        if (clock.year > 99) clock.year = 0; // 回到2000年
                    }
                }
            }
        }
    }
}

int main(void)
{
    char time_str[16], date_str[16], temp_str[8];

    board_init();
    uart1_init(115200U);

    OLED_Init();     // 初始化OLED
    OLED_Clear();

    // 显示启动信息
    OLED_ShowString(20, 24, (uint8_t *)"Digital", 16, 1);
    OLED_ShowString(28, 44, (uint8_t *)"Watch", 16, 1);
    OLED_Refresh();
    delay_ms(2000);

    while(1)
    {
        // 更新时钟
        UpdateClock();

        // 清屏
        OLED_Clear();

        // 显示边框装饰
        OLED_ShowString(0, 0, (uint8_t *)"=================", 8, 1);
        OLED_ShowString(0, 56, (uint8_t *)"=================", 8, 1);

        // 显示时间 (HH:MM:SS) - 大字体居中
        sprintf(time_str, "%02d:%02d:%02d",
                clock.hours, clock.minutes, clock.seconds);
        OLED_ShowString(8, 16, (uint8_t *)time_str, 16, 1);

        // 显示日期 (20YY-MM-DD)
        sprintf(date_str, "20%02d-%02d-%02d",
                clock.year, clock.month, clock.date);
        OLED_ShowString(20, 36, (uint8_t *)date_str, 12, 1);

        // 显示星期 - 右对齐
        OLED_ShowString(88, 36, (uint8_t *)weekdays[clock.weekday], 12, 1);

        // 显示秒数指示器 (小点闪烁)
        if (clock.seconds % 2 == 0) {
            OLED_ShowString(120, 8, (uint8_t *)"*", 8, 1);
        }

        OLED_Refresh();
        delay_ms(1000); // 每秒更新一次
    }
}
