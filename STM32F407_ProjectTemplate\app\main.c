/*
 * STM32F407 智能手表项目 - 宇航员版
 * 作者：LCKFB-LP
 * 网站：www.lckfb.com
 * 论坛：https://oshwhub.com/forum
 *
 * 功能特性：
 * - 开机动画效果
 * - 分区界面设计（左侧信息区 + 右侧动画区）
 * - 电池电量、室内温度、步数显示
 * - 心率监测和天气信息显示
 * - 旋转宇航员模型动画（16x16像素，4帧）
 * - 简化的主题边框和指示器
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-03-14     LCKFB-LP    first version
 * 2025-08-12     AI-Assistant astronaut version with rotating animation
 */
#include "board.h"
#include "bsp_uart.h"
#include <stdio.h>
#include <string.h>
#include <math.h>
#include "oled.h"

// 星期字符串数组
const char* weekdays[] = {"", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};

// 手表图标数据 (16x16)
const uint8_t watch_icon[] = {
    0x00,0x00,0x07,0xE0,0x18,0x18,0x20,0x04,0x47,0xE2,0x48,0x12,0x90,0x09,0x90,0x09,
    0x90,0x09,0x90,0x09,0x48,0x12,0x47,0xE2,0x20,0x04,0x18,0x18,0x07,0xE0,0x00,0x00
};

// 电池图标数据 (12x8)
const uint8_t battery_icon[] = {
    0x7E,0x42,0x42,0x42,0x42,0x42,0x42,0x7E,0x18,0x18,0x18,0x18
};

// 心形图标数据 (8x8)
const uint8_t heart_icon[] = {
    0x00,0x66,0xFF,0xFF,0x7E,0x3C,0x18,0x00
};

// 天气图标数据 (8x8)
const uint8_t weather_sunny[] = {    // 晴天
    0x18,0x18,0x7E,0xFF,0xFF,0x7E,0x18,0x18
};
const uint8_t weather_cloudy[] = {   // 多云
    0x00,0x3C,0x7E,0xFF,0xFF,0x7E,0x3C,0x00
};
const uint8_t weather_rainy[] = {    // 雨天
    0x3C,0x7E,0xFF,0xFF,0x18,0x18,0x18,0x18
};
const uint8_t weather_snowy[] = {    // 雪天
    0x42,0x24,0x18,0xFF,0xFF,0x18,0x24,0x42
};

// 宇航员模型数据 (12x12) - 8个旋转帧，更流畅的旋转动画
const uint8_t astronaut_frame0[] = {  // 0度 - 正面
    0x1E,0x00,0x33,0x00,0x33,0x00,0x1E,0x00,0x0C,0x00,0x1E,0x00,
    0x33,0x00,0x33,0x00,0x1E,0x00,0x0C,0x00,0x1E,0x00,0x33,0x00
};
const uint8_t astronaut_frame1[] = {  // 45度
    0x0F,0x00,0x19,0x80,0x31,0x80,0x1F,0x00,0x0E,0x00,0x1C,0x00,
    0x39,0x80,0x31,0x80,0x1F,0x00,0x0E,0x00,0x1C,0x00,0x39,0x80
};
const uint8_t astronaut_frame2[] = {  // 90度 - 右侧面
    0x07,0x80,0x0C,0xC0,0x18,0xC0,0x0F,0x80,0x07,0x00,0x0E,0x00,
    0x1C,0xC0,0x18,0xC0,0x0F,0x80,0x07,0x00,0x0E,0x00,0x1C,0xC0
};
const uint8_t astronaut_frame3[] = {  // 135度
    0x03,0xC0,0x06,0x60,0x0C,0x60,0x07,0xC0,0x03,0x80,0x07,0x00,
    0x0E,0x60,0x0C,0x60,0x07,0xC0,0x03,0x80,0x07,0x00,0x0E,0x60
};
const uint8_t astronaut_frame4[] = {  // 180度 - 背面
    0x01,0xE0,0x03,0x30,0x06,0x30,0x03,0xE0,0x01,0xC0,0x03,0x80,
    0x07,0x30,0x06,0x30,0x03,0xE0,0x01,0xC0,0x03,0x80,0x07,0x30
};
const uint8_t astronaut_frame5[] = {  // 225度
    0x00,0xF0,0x01,0x98,0x03,0x18,0x01,0xF0,0x00,0xE0,0x01,0xC0,
    0x03,0x98,0x03,0x18,0x01,0xF0,0x00,0xE0,0x01,0xC0,0x03,0x98
};
const uint8_t astronaut_frame6[] = {  // 270度 - 左侧面
    0x00,0x78,0x00,0xCC,0x01,0x8C,0x00,0xF8,0x00,0x70,0x00,0xE0,
    0x01,0xCC,0x01,0x8C,0x00,0xF8,0x00,0x70,0x00,0xE0,0x01,0xCC
};
const uint8_t astronaut_frame7[] = {  // 315度
    0x00,0x3C,0x00,0x66,0x00,0xC6,0x00,0x7C,0x00,0x38,0x00,0x70,
    0x00,0xE6,0x00,0xC6,0x00,0x7C,0x00,0x38,0x00,0x70,0x00,0xE6
};

// 软件时钟结构体
typedef struct {
    uint8_t hours;
    uint8_t minutes;
    uint8_t seconds;
    uint8_t year;    // 从2000年开始
    uint8_t month;   // 1-12
    uint8_t date;    // 1-31
    uint8_t weekday; // 1-7 (1=Monday)
} SoftClock_t;

SoftClock_t clock = {18, 20, 0, 25, 8, 12, 2}; // 2025年8月12日18:20:00 星期二

// 手表状态数据
typedef struct {
    uint8_t battery_level;    // 电池电量 0-100
    int8_t temperature;       // 温度 -99到99
    uint16_t steps;          // 步数计数
    uint8_t current_theme;   // 当前主题 0-2
    uint8_t notification;    // 通知标志
    uint8_t heart_rate;      // 心率 40-200
    int8_t weather_temp;     // 天气温度
    uint8_t weather_type;    // 天气类型 0=晴天 1=多云 2=雨天 3=雪天
} WatchStatus_t;

WatchStatus_t watch_status = {85, 23, 1234, 0, 0, 72, 25, 0}; // 初始状态

// 界面枚举（简化为单界面）
typedef enum {
    SCREEN_MAIN = 0,     // 主界面
    SCREEN_COUNT
} ScreenType_t;

uint8_t current_screen = SCREEN_MAIN;
uint8_t animation_frame = 0; // 动画帧计数器

// 判断是否为闰年
uint8_t IsLeapYear(uint16_t year) {
    return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0));
}

// 获取每月天数
uint8_t GetDaysInMonth(uint8_t month, uint16_t year) {
    const uint8_t days[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if (month == 2 && IsLeapYear(year)) return 29;
    return days[month];
}

// 绘制进度条
void DrawProgressBar(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t progress) {
    OLED_DrawLine(x, y, x + width, y, 1); // 上边框
    OLED_DrawLine(x, y + height, x + width, y + height, 1); // 下边框
    OLED_DrawLine(x, y, x, y + height, 1); // 左边框
    OLED_DrawLine(x + width, y, x + width, y + height, 1); // 右边框

    uint8_t fill_width = (width - 2) * progress / 100; // 填充宽度
    for (uint8_t i = 0; i < fill_width; i++) {
        for (uint8_t j = 1; j < height; j++) {
            OLED_DrawPoint(x + 1 + i, y + j, 1);
        }
    }
}

// 绘制电池图标和电量
void DrawBattery(uint8_t x, uint8_t y, uint8_t level) {
    // 电池外框
    OLED_DrawLine(x, y, x + 12, y, 1);
    OLED_DrawLine(x, y + 6, x + 12, y + 6, 1);
    OLED_DrawLine(x, y, x, y + 6, 1);
    OLED_DrawLine(x + 12, y, x + 12, y + 6, 1);
    // 电池正极
    OLED_DrawLine(x + 13, y + 2, x + 13, y + 4, 1);

    // 电量填充
    uint8_t fill_width = 10 * level / 100;
    for (uint8_t i = 0; i < fill_width; i++) {
        for (uint8_t j = 1; j < 6; j++) {
            OLED_DrawPoint(x + 1 + i, y + j, 1);
        }
    }
}

// 绘制装饰性边框
void DrawDecorativeBorder(uint8_t theme) {
    switch (theme) {
        case 0: // 简约主题
            OLED_DrawLine(0, 0, 127, 0, 1);
            OLED_DrawLine(0, 63, 127, 63, 1);
            break;
        case 1: // 圆角主题
            for (uint8_t i = 0; i < 128; i += 4) {
                OLED_DrawPoint(i, 0, 1);
                OLED_DrawPoint(i, 63, 1);
            }
            for (uint8_t i = 0; i < 64; i += 4) {
                OLED_DrawPoint(0, i, 1);
                OLED_DrawPoint(127, i, 1);
            }
            break;
        case 2: // 双线主题
            OLED_DrawLine(0, 0, 127, 0, 1);
            OLED_DrawLine(0, 1, 127, 1, 1);
            OLED_DrawLine(0, 62, 127, 62, 1);
            OLED_DrawLine(0, 63, 127, 63, 1);
            break;
    }
}

// 获取天气图标
const uint8_t* GetWeatherIcon(uint8_t weather_type) {
    switch (weather_type) {
        case 0: return weather_sunny;
        case 1: return weather_cloudy;
        case 2: return weather_rainy;
        case 3: return weather_snowy;
        default: return weather_sunny;
    }
}

// 获取宇航员动画帧（8帧旋转）
const uint8_t* GetAstronautFrame(uint8_t frame) {
    switch (frame % 8) {
        case 0: return astronaut_frame0; // 0度
        case 1: return astronaut_frame1; // 45度
        case 2: return astronaut_frame2; // 90度
        case 3: return astronaut_frame3; // 135度
        case 4: return astronaut_frame4; // 180度
        case 5: return astronaut_frame5; // 225度
        case 6: return astronaut_frame6; // 270度
        case 7: return astronaut_frame7; // 315度
        default: return astronaut_frame0;
    }
}

// 绘制旋转宇航员（带真实旋转效果）
void DrawRotatingAstronaut(uint8_t center_x, uint8_t center_y, uint8_t frame) {
    // 旋转角度计算 (0-7对应0-315度，每45度一帧)
    uint8_t current_frame = frame % 8;

    // 绘制当前帧的宇航员
    OLED_ShowPicture(center_x - 6, center_y - 6, 12, 12,
                     (uint8_t*)GetAstronautFrame(current_frame), 1);

    // 添加旋转轨迹效果（可选）
    if (frame % 2 == 0) {
        // 在宇航员周围画一个轨迹圆圈
        for (uint8_t i = 0; i < 8; i++) {
            uint8_t trail_x = center_x + (6 * (i % 4)) / 4 - 3;
            uint8_t trail_y = center_y + (6 * ((i + 2) % 4)) / 4 - 3;
            if (trail_x < 128 && trail_y < 64) {
                OLED_DrawPoint(trail_x, trail_y, 1);
            }
        }
    }
}

// 更新手表状态（模拟数据变化）
void UpdateWatchStatus(void) {
    static uint16_t counter = 0;
    counter++;

    // 模拟电池电量缓慢下降
    if (counter % 3600 == 0 && watch_status.battery_level > 0) {
        watch_status.battery_level--;
    }

    // 模拟温度变化
    if (counter % 30 == 0) {
        watch_status.temperature = 20 + (counter / 30) % 10;
    }

    // 模拟步数增加
    if (counter % 5 == 0) {
        watch_status.steps += (counter % 3) + 1;
        if (watch_status.steps > 9999) watch_status.steps = 0;
    }

    // 模拟心率变化
    if (counter % 10 == 0) {
        watch_status.heart_rate = 65 + (counter / 10) % 25; // 65-90 BPM
    }

    // 模拟天气变化
    if (counter % 120 == 0) { // 每2分钟变化一次天气
        watch_status.weather_type = (counter / 120) % 4;
        watch_status.weather_temp = 18 + (counter / 120) % 15; // 18-32度
    }
}

// 显示主界面（包含心率、天气和旋转宇航员）
void DisplayMainScreen(void) {
    char time_str[16], date_str[20], temp_str[12];

    // 简化边框 - 只显示上下边线
    OLED_DrawLine(0, 0, 127, 0, 1);
    OLED_DrawLine(0, 63, 127, 63, 1);

    // 右侧分隔线，为宇航员留出空间
    OLED_DrawLine(95, 0, 95, 63, 1);

    // 第一行：温度和电池（左侧区域）
    sprintf(temp_str, "%d°C", watch_status.temperature);
    OLED_ShowString(2, 2, (uint8_t *)temp_str, 8, 1);

    sprintf(temp_str, "BAT:%d%%", watch_status.battery_level);
    OLED_ShowString(50, 2, (uint8_t *)temp_str, 8, 1);

    // 第二行：时间显示 - 左侧区域
    sprintf(time_str, "%02d:%02d:%02d",
            clock.hours, clock.minutes, clock.seconds);
    OLED_ShowString(4, 16, (uint8_t *)time_str, 12, 1); // 使用12号字体节省空间

    // 第三行：日期和星期（左侧区域）
    sprintf(date_str, "%02d-%02d %s",
            clock.month, clock.date, weekdays[clock.weekday]);
    OLED_ShowString(4, 30, (uint8_t *)date_str, 8, 1);

    // 第四行：步数（左侧区域）
    sprintf(temp_str, "Steps:%d", watch_status.steps);
    OLED_ShowString(2, 42, (uint8_t *)temp_str, 8, 1);

    // 第五行：心率（左侧区域）
    OLED_ShowPicture(2, 52, 8, 8, (uint8_t*)heart_icon, 1);
    sprintf(temp_str, "%d BPM", watch_status.heart_rate);
    OLED_ShowString(12, 52, (uint8_t *)temp_str, 8, 1);

    // 第六行：天气信息（左侧区域）
    OLED_ShowPicture(50, 52, 8, 8, (uint8_t*)GetWeatherIcon(watch_status.weather_type), 1);
    sprintf(temp_str, "%d°C", watch_status.weather_temp);
    OLED_ShowString(60, 52, (uint8_t *)temp_str, 8, 1);

    // 右侧：旋转宇航员模型（12x12像素，8帧旋转）
    uint8_t astronaut_frame = (animation_frame / 8) % 8; // 每8帧切换一次，更快的旋转
    DrawRotatingAstronaut(110, 30, astronaut_frame);

    // 宇航员下方的小标签
    OLED_ShowString(100, 44, (uint8_t *)"ASTRO", 8, 1);

    // 添加旋转方向指示器
    OLED_ShowString(98, 52, (uint8_t *)"ROTATING", 8, 1);
}





// 开机动画
void BootAnimation(void) {
    // 第一阶段：显示手表图标
    OLED_Clear();
    OLED_ShowPicture(56, 16, 16, 16, (uint8_t*)watch_icon, 1);
    OLED_ShowString(32, 40, (uint8_t*)"Smart Watch", 12, 1);
    OLED_Refresh();
    delay_ms(1500);

    // 第二阶段：进度条动画
    OLED_Clear();
    OLED_ShowString(40, 16, (uint8_t*)"Loading...", 12, 1);

    for (uint8_t i = 0; i <= 100; i += 10) {
        DrawProgressBar(20, 35, 88, 8, i);
        OLED_Refresh();
        delay_ms(150);
        if (i < 100) {
            // 清除进度条区域准备下次绘制
            for (uint8_t x = 21; x < 107; x++) {
                for (uint8_t y = 36; y < 42; y++) {
                    OLED_DrawPoint(x, y, 0);
                }
            }
        }
    }

    delay_ms(500);

    // 第三阶段：欢迎信息淡入效果
    OLED_Clear();
    OLED_ShowString(28, 20, (uint8_t*)"Welcome!", 16, 1);
    OLED_ShowString(20, 40, (uint8_t*)"Enjoy your day", 12, 1);
    OLED_Refresh();
    delay_ms(2000);

    // 淡出效果（简单的闪烁模拟）
    for (uint8_t i = 0; i < 3; i++) {
        OLED_Clear();
        OLED_Refresh();
        delay_ms(200);
        OLED_ShowString(28, 20, (uint8_t*)"Welcome!", 16, 1);
        OLED_ShowString(20, 40, (uint8_t*)"Enjoy your day", 12, 1);
        OLED_Refresh();
        delay_ms(200);
    }
}

// 更新时钟
void UpdateClock(void) {
    clock.seconds++;
    if (clock.seconds >= 60) {
        clock.seconds = 0;
        clock.minutes++;
        if (clock.minutes >= 60) {
            clock.minutes = 0;
            clock.hours++;
            if (clock.hours >= 24) {
                clock.hours = 0;
                clock.date++;
                clock.weekday++;
                if (clock.weekday > 7) clock.weekday = 1;

                uint8_t days_in_month = GetDaysInMonth(clock.month, 2000 + clock.year);
                if (clock.date > days_in_month) {
                    clock.date = 1;
                    clock.month++;
                    if (clock.month > 12) {
                        clock.month = 1;
                        clock.year++;
                        if (clock.year > 99) clock.year = 0; // 回到2000年
                    }
                }
            }
        }
    }
}

int main(void)
{
    char time_str[16], date_str[16], temp_str[8];

    board_init();
    uart1_init(115200U);

    OLED_Init();     // 初始化OLED
    OLED_Clear();

    // 播放开机动画
    BootAnimation();

    while(1)
    {
        // 更新时钟和状态
        UpdateClock();
        UpdateWatchStatus();

        // 更新动画帧（简化）
        animation_frame++;
        if (animation_frame >= 60) animation_frame = 0; // 减少计数范围

        // 清屏
        OLED_Clear();

        // 只显示主界面
        DisplayMainScreen();

        // 主题切换（每60秒切换一次，减少频率）
        static uint8_t theme_counter = 0;
        theme_counter++;
        if (theme_counter >= 60) {
            theme_counter = 0;
            watch_status.current_theme = (watch_status.current_theme + 1) % 3;
        }

        OLED_Refresh();
        delay_ms(1000); // 每秒更新一次
    }
}
