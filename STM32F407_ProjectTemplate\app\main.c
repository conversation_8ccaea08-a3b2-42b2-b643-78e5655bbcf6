/*
 * ������������Ӳ�������������չ����Ӳ�����Ϲ���ȫ����Դ
 * �����������www.lckfb.com
 * ����֧�ֳ�פ��̳���κμ������⻶ӭ��ʱ����ѧϰ
 * ������̳��https://oshwhub.com/forum
 * ��עbilibili�˺ţ������������塿���������ǵ����¶�̬��
 * ��������׬Ǯ���������й�����ʦΪ����
 * 

 Change Logs:
 * Date           Author       Notes
 * 2024-03-14     LCKFB-LP    first version
 */
#include "board.h"
#include "bsp_uart.h"
#include <stdio.h>
#include <string.h>
#include <math.h>
#include "oled.h"

// 星期字符串数组
const char* weekdays[] = {"", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Thu", "<PERSON><PERSON>", "Sat", "Sun"};

// 手表图标数据 (16x16)
const uint8_t watch_icon[] = {
    0x00,0x00,0x07,0xE0,0x18,0x18,0x20,0x04,0x47,0xE2,0x48,0x12,0x90,0x09,0x90,0x09,
    0x90,0x09,0x90,0x09,0x48,0x12,0x47,0xE2,0x20,0x04,0x18,0x18,0x07,0xE0,0x00,0x00
};

// 电池图标数据 (12x8)
const uint8_t battery_icon[] = {
    0x7E,0x42,0x42,0x42,0x42,0x42,0x42,0x7E,0x18,0x18,0x18,0x18
};

// 心形图标数据 (8x8)
const uint8_t heart_icon[] = {
    0x00,0x66,0xFF,0xFF,0x7E,0x3C,0x18,0x00
};

// 软件时钟结构体
typedef struct {
    uint8_t hours;
    uint8_t minutes;
    uint8_t seconds;
    uint8_t year;    // 从2000年开始
    uint8_t month;   // 1-12
    uint8_t date;    // 1-31
    uint8_t weekday; // 1-7 (1=Monday)
} SoftClock_t;

SoftClock_t clock = {18, 20, 0, 25, 8, 12, 2}; // 2025年8月12日18:20:00 星期二

// 手表状态数据
typedef struct {
    uint8_t battery_level;    // 电池电量 0-100
    int8_t temperature;       // 温度 -99到99
    uint16_t steps;          // 步数计数
    uint8_t current_theme;   // 当前主题 0-2
    uint8_t notification;    // 通知标志
} WatchStatus_t;

WatchStatus_t watch_status = {85, 23, 1234, 0, 0}; // 初始状态

// 界面枚举
typedef enum {
    SCREEN_MAIN = 0,     // 主界面
    SCREEN_INFO,         // 信息界面
    SCREEN_SETTINGS,     // 设置界面
    SCREEN_COUNT
} ScreenType_t;

uint8_t current_screen = SCREEN_MAIN;
uint8_t screen_switch_counter = 0;
uint8_t animation_frame = 0; // 动画帧计数器

// 判断是否为闰年
uint8_t IsLeapYear(uint16_t year) {
    return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0));
}

// 获取每月天数
uint8_t GetDaysInMonth(uint8_t month, uint16_t year) {
    const uint8_t days[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if (month == 2 && IsLeapYear(year)) return 29;
    return days[month];
}

// 绘制进度条
void DrawProgressBar(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t progress) {
    OLED_DrawLine(x, y, x + width, y, 1); // 上边框
    OLED_DrawLine(x, y + height, x + width, y + height, 1); // 下边框
    OLED_DrawLine(x, y, x, y + height, 1); // 左边框
    OLED_DrawLine(x + width, y, x + width, y + height, 1); // 右边框

    uint8_t fill_width = (width - 2) * progress / 100; // 填充宽度
    for (uint8_t i = 0; i < fill_width; i++) {
        for (uint8_t j = 1; j < height; j++) {
            OLED_DrawPoint(x + 1 + i, y + j, 1);
        }
    }
}

// 绘制电池图标和电量
void DrawBattery(uint8_t x, uint8_t y, uint8_t level) {
    // 电池外框
    OLED_DrawLine(x, y, x + 12, y, 1);
    OLED_DrawLine(x, y + 6, x + 12, y + 6, 1);
    OLED_DrawLine(x, y, x, y + 6, 1);
    OLED_DrawLine(x + 12, y, x + 12, y + 6, 1);
    // 电池正极
    OLED_DrawLine(x + 13, y + 2, x + 13, y + 4, 1);

    // 电量填充
    uint8_t fill_width = 10 * level / 100;
    for (uint8_t i = 0; i < fill_width; i++) {
        for (uint8_t j = 1; j < 6; j++) {
            OLED_DrawPoint(x + 1 + i, y + j, 1);
        }
    }
}

// 绘制装饰性边框
void DrawDecorativeBorder(uint8_t theme) {
    switch (theme) {
        case 0: // 简约主题
            OLED_DrawLine(0, 0, 127, 0, 1);
            OLED_DrawLine(0, 63, 127, 63, 1);
            break;
        case 1: // 圆角主题
            for (uint8_t i = 0; i < 128; i += 4) {
                OLED_DrawPoint(i, 0, 1);
                OLED_DrawPoint(i, 63, 1);
            }
            for (uint8_t i = 0; i < 64; i += 4) {
                OLED_DrawPoint(0, i, 1);
                OLED_DrawPoint(127, i, 1);
            }
            break;
        case 2: // 双线主题
            OLED_DrawLine(0, 0, 127, 0, 1);
            OLED_DrawLine(0, 1, 127, 1, 1);
            OLED_DrawLine(0, 62, 127, 62, 1);
            OLED_DrawLine(0, 63, 127, 63, 1);
            break;
    }
}

// 更新手表状态（模拟数据变化）
void UpdateWatchStatus(void) {
    static uint16_t counter = 0;
    counter++;

    // 模拟电池电量缓慢下降
    if (counter % 3600 == 0 && watch_status.battery_level > 0) {
        watch_status.battery_level--;
    }

    // 模拟温度变化
    if (counter % 30 == 0) {
        watch_status.temperature = 20 + (counter / 30) % 10;
    }

    // 模拟步数增加
    if (counter % 5 == 0) {
        watch_status.steps += (counter % 3) + 1;
        if (watch_status.steps > 9999) watch_status.steps = 0;
    }
}

// 显示主界面
void DisplayMainScreen(void) {
    char time_str[16], date_str[32], temp_str[16];

    // 绘制装饰性边框
    DrawDecorativeBorder(watch_status.current_theme);

    // 显示电池电量 - 右上角
    DrawBattery(100, 2, watch_status.battery_level);
    sprintf(temp_str, "%d%%", watch_status.battery_level);
    OLED_ShowString(105, 10, (uint8_t *)temp_str, 8, 1);

    // 显示温度 - 左上角
    sprintf(temp_str, "%d°C", watch_status.temperature);
    OLED_ShowString(2, 2, (uint8_t *)temp_str, 8, 1);

    // 显示时间 (HH:MM:SS) - 大字体居中
    sprintf(time_str, "%02d:%02d:%02d",
            clock.hours, clock.minutes, clock.seconds);
    OLED_ShowString(8, 20, (uint8_t *)time_str, 16, 1);

    // 显示日期和星期
    sprintf(date_str, "20%02d-%02d-%02d %s",
            clock.year, clock.month, clock.date, weekdays[clock.weekday]);
    OLED_ShowString(8, 40, (uint8_t *)date_str, 12, 1);

    // 显示步数 - 底部左侧
    sprintf(temp_str, "Steps:%d", watch_status.steps);
    OLED_ShowString(2, 52, (uint8_t *)temp_str, 8, 1);

    // 显示心率图标和模拟心率 - 底部右侧
    OLED_ShowPicture(90, 52, 8, 8, (uint8_t*)heart_icon, 1);
    sprintf(temp_str, "72");
    OLED_ShowString(100, 52, (uint8_t *)temp_str, 8, 1);

    // 动态秒数指示器 (多种效果)
    switch (animation_frame % 4) {
        case 0:
            OLED_DrawCircle(120, 25, 2);
            break;
        case 1:
            OLED_DrawCircle(120, 25, 3);
            break;
        case 2:
            OLED_DrawPoint(120, 25, 1);
            OLED_DrawPoint(119, 25, 1);
            OLED_DrawPoint(121, 25, 1);
            OLED_DrawPoint(120, 24, 1);
            OLED_DrawPoint(120, 26, 1);
            break;
        case 3:
            DrawSpinnerAnimation(120, 25, animation_frame);
            break;
    }
}

// 显示信息界面
void DisplayInfoScreen(void) {
    char temp_str[32];

    OLED_ShowString(35, 2, (uint8_t *)"INFO", 16, 1);
    OLED_DrawLine(0, 18, 127, 18, 1);

    // 显示详细信息
    sprintf(temp_str, "Battery: %d%%", watch_status.battery_level);
    OLED_ShowString(5, 22, (uint8_t *)temp_str, 12, 1);

    sprintf(temp_str, "Temperature: %d°C", watch_status.temperature);
    OLED_ShowString(5, 34, (uint8_t *)temp_str, 12, 1);

    sprintf(temp_str, "Steps: %d", watch_status.steps);
    OLED_ShowString(5, 46, (uint8_t *)temp_str, 12, 1);

    // 显示运行时间
    uint32_t total_seconds = clock.hours * 3600 + clock.minutes * 60 + clock.seconds;
    sprintf(temp_str, "Uptime: %lds", total_seconds);
    OLED_ShowString(5, 58, (uint8_t *)temp_str, 8, 1);
}

// 显示设置界面
void DisplaySettingsScreen(void) {
    char temp_str[32];

    OLED_ShowString(25, 2, (uint8_t *)"SETTINGS", 16, 1);
    OLED_DrawLine(0, 18, 127, 18, 1);

    // 显示设置选项
    sprintf(temp_str, "Theme: %d", watch_status.current_theme + 1);
    OLED_ShowString(5, 22, (uint8_t *)temp_str, 12, 1);

    OLED_ShowString(5, 34, (uint8_t *)"Auto Switch: ON", 12, 1);

    sprintf(temp_str, "Version: v1.2");
    OLED_ShowString(5, 46, (uint8_t *)temp_str, 12, 1);

    OLED_ShowString(5, 58, (uint8_t *)"Smart Watch OS", 8, 1);
}

// 绘制动画波形（简化版本，不使用sin函数）
void DrawWaveAnimation(uint8_t y, uint8_t frame) {
    static const int8_t wave_table[16] = {0, 1, 2, 3, 3, 2, 1, 0, -1, -2, -3, -3, -2, -1, 0, 1};

    for (uint8_t x = 0; x < 128; x += 4) {
        uint8_t wave_index = (x / 4 + frame) % 16;
        int8_t wave_offset = wave_table[wave_index];
        uint8_t wave_y = y + wave_offset;

        if (wave_y < 64) {
            OLED_DrawPoint(x, wave_y, 1);
        }
    }
}

// 绘制旋转指针动画
void DrawSpinnerAnimation(uint8_t center_x, uint8_t center_y, uint8_t frame) {
    uint8_t angle = frame * 15; // 每帧旋转15度
    uint8_t radius = 8;

    // 简化的旋转计算（使用查找表或近似）
    uint8_t x_offset = (radius * (angle % 4)) / 4;
    uint8_t y_offset = (radius * ((angle + 1) % 4)) / 4;

    OLED_DrawLine(center_x, center_y, center_x + x_offset, center_y + y_offset, 1);
    OLED_DrawCircle(center_x, center_y, 2);
}

// 滚动文字显示
void DrawScrollingText(uint8_t y, const char* text, uint8_t frame) {
    char display_text[32];
    uint8_t text_len = strlen(text);
    uint8_t scroll_pos = frame % (text_len + 16); // 16是屏幕字符宽度

    if (scroll_pos < 16) {
        // 文字从右侧进入
        sprintf(display_text, "%*s%s", 16 - scroll_pos, "", text);
    } else {
        // 文字向左滚动
        uint8_t start_pos = scroll_pos - 16;
        if (start_pos < text_len) {
            strncpy(display_text, text + start_pos, 16);
            display_text[16] = '\0';
        } else {
            strcpy(display_text, "");
        }
    }

    OLED_ShowString(0, y, (uint8_t *)display_text, 8, 1);
}

// 绘制数字雨效果
void DrawDigitalRain(uint8_t frame) {
    static uint8_t rain_columns[16] = {0}; // 16列

    for (uint8_t col = 0; col < 16; col++) {
        rain_columns[col] = (rain_columns[col] + 1 + col) % 64;

        // 绘制数字字符
        char digit = '0' + (frame + col) % 10;
        OLED_ShowChar(col * 8, rain_columns[col], digit, 8, 1);

        // 添加拖尾效果
        if (rain_columns[col] > 8) {
            char trail_digit = '0' + (frame + col + 1) % 10;
            OLED_ShowChar(col * 8, rain_columns[col] - 8, trail_digit, 8, 0);
        }
    }
}

// 开机动画
void BootAnimation(void) {
    // 第一阶段：显示手表图标
    OLED_Clear();
    OLED_ShowPicture(56, 16, 16, 16, (uint8_t*)watch_icon, 1);
    OLED_ShowString(32, 40, (uint8_t*)"Smart Watch", 12, 1);
    OLED_Refresh();
    delay_ms(1500);

    // 第二阶段：进度条动画
    OLED_Clear();
    OLED_ShowString(40, 16, (uint8_t*)"Loading...", 12, 1);

    for (uint8_t i = 0; i <= 100; i += 10) {
        DrawProgressBar(20, 35, 88, 8, i);
        OLED_Refresh();
        delay_ms(150);
        if (i < 100) {
            // 清除进度条区域准备下次绘制
            for (uint8_t x = 21; x < 107; x++) {
                for (uint8_t y = 36; y < 42; y++) {
                    OLED_DrawPoint(x, y, 0);
                }
            }
        }
    }

    delay_ms(500);

    // 第三阶段：欢迎信息淡入效果
    OLED_Clear();
    OLED_ShowString(28, 20, (uint8_t*)"Welcome!", 16, 1);
    OLED_ShowString(20, 40, (uint8_t*)"Enjoy your day", 12, 1);
    OLED_Refresh();
    delay_ms(2000);

    // 淡出效果（简单的闪烁模拟）
    for (uint8_t i = 0; i < 3; i++) {
        OLED_Clear();
        OLED_Refresh();
        delay_ms(200);
        OLED_ShowString(28, 20, (uint8_t*)"Welcome!", 16, 1);
        OLED_ShowString(20, 40, (uint8_t*)"Enjoy your day", 12, 1);
        OLED_Refresh();
        delay_ms(200);
    }
}

// 更新时钟
void UpdateClock(void) {
    clock.seconds++;
    if (clock.seconds >= 60) {
        clock.seconds = 0;
        clock.minutes++;
        if (clock.minutes >= 60) {
            clock.minutes = 0;
            clock.hours++;
            if (clock.hours >= 24) {
                clock.hours = 0;
                clock.date++;
                clock.weekday++;
                if (clock.weekday > 7) clock.weekday = 1;

                uint8_t days_in_month = GetDaysInMonth(clock.month, 2000 + clock.year);
                if (clock.date > days_in_month) {
                    clock.date = 1;
                    clock.month++;
                    if (clock.month > 12) {
                        clock.month = 1;
                        clock.year++;
                        if (clock.year > 99) clock.year = 0; // 回到2000年
                    }
                }
            }
        }
    }
}

int main(void)
{
    char time_str[16], date_str[16], temp_str[8];

    board_init();
    uart1_init(115200U);

    OLED_Init();     // 初始化OLED
    OLED_Clear();

    // 播放开机动画
    BootAnimation();

    while(1)
    {
        // 更新时钟和状态
        UpdateClock();
        UpdateWatchStatus();

        // 更新动画帧
        animation_frame++;
        if (animation_frame >= 240) animation_frame = 0; // 防止溢出

        // 清屏
        OLED_Clear();

        // 界面切换逻辑 (每10秒切换一次界面)
        screen_switch_counter++;
        if (screen_switch_counter >= 10) {
            screen_switch_counter = 0;
            current_screen = (current_screen + 1) % SCREEN_COUNT;
        }

        // 根据当前界面显示内容
        switch (current_screen) {
            case SCREEN_MAIN:
                DisplayMainScreen();
                break;
            case SCREEN_INFO:
                DisplayInfoScreen();
                // 添加波形动画效果
                DrawWaveAnimation(58, animation_frame / 4);
                break;
            case SCREEN_SETTINGS:
                DisplaySettingsScreen();
                // 添加数字雨效果
                if (animation_frame % 8 == 0) {
                    DrawDigitalRain(animation_frame / 8);
                }
                break;
        }

        // 主题切换指示 (每30秒切换一次主题)
        static uint8_t theme_counter = 0;
        theme_counter++;
        if (theme_counter >= 30) {
            theme_counter = 0;
            watch_status.current_theme = (watch_status.current_theme + 1) % 3;
        }

        OLED_Refresh();
        delay_ms(1000); // 每秒更新一次
    }
}
